variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "The environment (e.g., dev, prod)"
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

variable "xray_traces_bucket_name" {
  description = ""
  type        = string
}

variable "xray_data_s3_folder" {
  description = ""
  type        = string
}

variable "api_id" {
  description = ""
  type        = string
}

variable "r3_oaf_service_api_id" {
  description = ""
  type        = string
}

variable "eip_service_api_id" {
  description = ""
  type        = string
}

variable "student_details_api_id" {
  description = ""
  type        = string
}

variable "apphero_api_id" {
  description = ""
  type        = string
}

variable "consumer_config_table" {
  description = ""
  type        = string
}

variable "api_xraytraces_function_name" {
  description = ""
  type        = string
}

variable "cloudwatch_event_rule_lambda_schedule_name" {
  description = ""
  type        = string
}

variable "cloudwatch_event_rule_schedule_expression" {
  description = ""
  type        = string
}

variable "cloudwatch_event_rule_schedule_xray_lambda_target_id" {
  description = ""
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}
