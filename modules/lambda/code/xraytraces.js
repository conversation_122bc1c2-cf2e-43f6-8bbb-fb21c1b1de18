const AWS = require("aws-sdk");
const parquet = require("parquetjs");
const dynamoDB = new AWS.DynamoDB.DocumentClient();
const s3 = new AWS.S3();
const path = require("path");
const fs = require("fs");

module.exports.xrayToS3 = async () => {
  try {
    const startTime = new Date(Date.now() - 4 * 60 * 1000);
    const endTime = new Date(Date.now());
    const apiIds = [process.env.APIGGATEWAY_API_ID, process.env.R3_OAF_SERVICE_API_ID, process.env.APPHERO_API_ID, process.env.EIP_API_ID, process.env.STUDENT_DETAILS_API_ID];
    const gusTraceBucket = process.env.GUS_TRACES_BUCKET;
    for (const apiId of apiIds) {
    const params = {
      StartTime: startTime,
      EndTime: endTime,
      FilterExpression: `annotation.aws:api_id = "${apiId}"`,
    };
    console.log('xray params', params);
    const xray = new AWS.XRay({ region: process.env.region });
    const xrayResponse = await xray.getTraceSummaries(params).promise();
    console.log('xrayResponse', xrayResponse)
    const { TraceSummaries, ApproximateTime } = xrayResponse;

    console.log("TraceSummaries", TraceSummaries);

    const dateObj = new Date(ApproximateTime);
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, "0");
    const day = String(dateObj.getDate()).padStart(2, "0");
    const epochTimestamp = dateObj.getTime();
    const hours = String(dateObj.getHours()).padStart(2, "0");
    const minutes = String(dateObj.getMinutes()).padStart(2, "0");
    const seconds = String(dateObj.getSeconds()).padStart(2, "0");

    for (let trace in TraceSummaries) {
      if (TraceSummaries[trace].Annotations.api_key) {
        const params = {
          TableName: process.env.CONSUMER_CONFIG_TABLE,
          Key: {
            PK: TraceSummaries[trace].Annotations.api_key[0].AnnotationValue
              .StringValue,
          },
        };
        const data = await dynamoDB.get(params).promise();
        TraceSummaries[trace]["Consumer"] = data.Item.consumer;
        TraceSummaries[trace]["Annotations_api_key"] =
          TraceSummaries[
            trace
          ].Annotations.api_key[0].AnnotationValue.StringValue;
      }
    }

    if (TraceSummaries && TraceSummaries.length > 0) {
      const bucketExists = await s3
        .headBucket({ Bucket: gusTraceBucket })
        .promise()
        .catch(() => false);
      if (!bucketExists) {
        await s3.createBucket({ Bucket: gusTraceBucket }).promise();
      }

      const flattenedTraces = flattenObjectsAndArrays(TraceSummaries);
      const modifiedTraces = flattenedTraces.map((trace) => ({
        ...trace,
        epochTimestamp,
      }));

      const filteredTraces = modifiedTraces.map((trace) => {
        return {
          id: trace.Id,
          duration: trace.Duration,
          responseTime: trace.ResponseTime,
          hasfault: trace.HasFault,
          haserror: trace.HasError,
          hasthrottle: trace.HasThrottle,
          ispartial: trace.IsPartial,
          http_httpurl: trace.Http_HttpURL,
          http_httpstatus: trace.Http_HttpStatus,
          http_httpmethod: trace.Http_HttpMethod,
          http_useragent: trace.Http_UserAgent,
          http_clientip: trace.Http_ClientIp,
          entrypoint_name: trace.EntryPoint_Name,
          entrypoint_names: trace.EntryPoint_Names,
          entrypoint_accountid: trace.EntryPoint_AccountId,
          entrypoint_type: trace.EntryPoint_Type,
          revision: trace.Annotations_revision,
          consumer: trace.Consumer,
          matchedeventtime: trace.MatchedEventTime,
          annotations_api_key: trace.Annotations_api_key,
          epochtimestamp: epochTimestamp,
          day: day,
          month: month,
          year: year,
        };
      });
      console.log(filteredTraces);

      const schema = new parquet.ParquetSchema({
        id: { type: "UTF8" },
        duration: { type: "FLOAT" },
        responseTime: { type: "FLOAT" },
        hasfault: { type: "BOOLEAN" },
        haserror: { type: "BOOLEAN" },
        hasthrottle: { type: "BOOLEAN" },
        ispartial: { type: "BOOLEAN" },
        http_httpurl: { type: "UTF8" },
        http_httpstatus: { type: "INT32" },
        http_httpmethod: { type: "UTF8" },
        http_useragent: { type: "UTF8" },
        http_clientip: { type: "UTF8" },
        entrypoint_name: { type: "UTF8" },
        entrypoint_names: { type: "UTF8" },
        entrypoint_accountid: { type: "UTF8", optional: true },
        entrypoint_type: { type: "UTF8" },
        revision: { type: "UTF8", optional: true },
        consumer: { type: "UTF8", optional: true },
        matchedeventtime: { type: "UTF8", optional: true },
        annotations_api_key: { type: "UTF8", optional: true },
        epochtimestamp: { type: "INT64" },
        day: { type: "UTF8" },
        month: { type: "UTF8" },
        year: { type: "INT32" },
      });

      const tempFilePath = path.join("/tmp", `${epochTimestamp}_data.parquet`);
      const writer = await parquet.ParquetWriter.openFile(schema, tempFilePath);
      for (const trace of filteredTraces) {
        writer.appendRow(trace);
      }
      await writer.close();

      console.log("filteredTraces");
      const folderName = process.env.XRAY_S3_FOLDER;
      const putObjectParams = {
        Bucket: gusTraceBucket,
        Key: `${folderName}/${epochTimestamp}_data.parquet`,
        Body: fs.createReadStream(tempFilePath),
      };
      try {
        await s3.putObject(putObjectParams).promise();
        console.log("Parquet file uploaded successfully to S3.");
      } catch (error) {
        console.error("Error uploading Parquet file to S3:", error);
      }
      console.log("Written");
      fs.unlinkSync(tempFilePath);
    }

    console.log("X-ray segment successfully written to S3.");
  }
    return {
      statusCode: 200,
      body: "X-ray segment successfully written to S3.",
    };
  } catch (error) {
    console.error("Error writing X-ray segment to S3:", error);
    return {
      statusCode: 500,
      body: "Error writing X-ray segment to S3.",
    };
  }
};

function flattenObjectsAndArrays(data) {
  if (Array.isArray(data)) {
    return data.map(flattenObjectsAndArrays);
  } else if (typeof data === "object" && data !== null) {
    const flattenedObject = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        if (Array.isArray(value)) {
          flattenedObject[key] = value.map(flattenObjectsAndArrays).join(", ");
        } else if (typeof value === "object" && value !== null) {
          const nestedObject = flattenObjectsAndArrays(value);
          for (const nestedKey in nestedObject) {
            if (nestedObject.hasOwnProperty(nestedKey)) {
              flattenedObject[`${key}_${nestedKey}`] = nestedObject[nestedKey];
            }
          }
        } else {
          flattenedObject[key] = value;
        }
      }
    }
    return flattenedObject;
  } else {
    return data;
  }
}

async function convertToParquet(traces) {
  const schema = new parquet.ParquetSchema({
    id: { type: "UTF8" },
    duration: { type: "FLOAT" },
    responseTime: { type: "FLOAT" },
    hasfault: { type: "BOOLEAN" },
    haserror: { type: "BOOLEAN" },
    hasthrottle: { type: "BOOLEAN" },
    ispartial: { type: "BOOLEAN" },
    http_httpurl: { type: "UTF8" },
    http_httpstatus: { type: "INT32" },
    http_httpmethod: { type: "UTF8" },
    http_useragent: { type: "UTF8" },
    http_clientip: { type: "UTF8" },
    entrypoint_name: { type: "UTF8" },
    entrypoint_names: { type: "UTF8" },
    entrypoint_accountid: { type: "UTF8", optional: true },
    entrypoint_type: { type: "UTF8" },
    revision: { type: "UTF8", optional: true },
    consumer: { type: "UTF8", optional: true },
    matchedeventtime: { type: "UTF8", optional: true },
    annotations_api_key: { type: "UTF8", optional: true },
    epochtimestamp: { type: "INT64" },
    day: { type: "UTF8" },
    month: { type: "UTF8" },
    year: { type: "INT32" },
  });

  const writer = await parquet.ParquetWriter.openFile(schema, "temp.parquet");
  for (const trace of traces) {
    writer.appendRow(trace);
  }
  await writer.close();
  const tempFileData = await readFile("temp.parquet");
  return tempFileData;
}

function readFile(fileName) {
  return new Promise((resolve, reject) => {
    const fs = require("fs");
    fs.readFile(fileName, (error, data) => {
      if (error) {
        reject(error);
      } else {
        resolve(data);
      }
    });
  });
}
