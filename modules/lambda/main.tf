provider "aws" {
  region = var.region
}

data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/code"
  output_path = "${path.module}/code-${replace(timestamp(), "/[^0-9]/", "")}.zip"
}

resource "aws_lambda_layer_version" "gus_eip_infra_layer" {
	  layer_name          = "gus-eip-infra-${var.environment}"
	  description         = "custom layer for gus-eip-infra"
	  filename            =  "${path.root}/layers/layers.zip"
	  compatible_runtimes = ["nodejs18.x"]

}
resource "aws_lambda_layer_version" "gus_eip_infra_layer2" {
	  layer_name          = "gus-eip-infra-layer2-${var.environment}"
	  description         = "custom layer for gus-eip-infra"
	  filename            =  "${path.root}/layers2/layers2.zip"
	  compatible_runtimes = ["nodejs18.x"]

}

resource "aws_lambda_function" "gus_xray_traces" {
  function_name = "${var.api_xraytraces_function_name}-${var.environment}"
  handler       = "xraytraces.xrayToS3"
  runtime       = "nodejs18.x"
  role          = "arn:aws:iam::${var.accountId}:role/gus-lambda-exec-role-${var.environment}" //gus-lambda-exec-role-${var.environment}
  filename      = data.archive_file.lambda_zip.output_path
  memory_size   = 300
  layers        = [aws_lambda_layer_version.gus_eip_infra_layer.arn, aws_lambda_layer_version.gus_eip_infra_layer2.arn]

  environment {
    variables = {
      GUS_TRACES_BUCKET: var.xray_traces_bucket_name,
      XRAY_S3_FOLDER: var.xray_data_s3_folder
      APIGGATEWAY_API_ID: var.api_id
      R3_OAF_SERVICE_API_ID:var.r3_oaf_service_api_id
      region: var.region 
      CONSUMER_CONFIG_TABLE: var.consumer_config_table
      APPHERO_API_ID: var.apphero_api_id
      EIP_API_ID: var.eip_service_api_id
      STUDENT_DETAILS_API_ID: var.student_details_api_id
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}


#event schedule
resource "aws_cloudwatch_event_rule" "lambda_schedule" {
    name = var.cloudwatch_event_rule_lambda_schedule_name
    description = "Schedule for Lambda Function"
    schedule_expression = var.cloudwatch_event_rule_schedule_expression
}

resource "aws_cloudwatch_event_target" "schedule_xray_lambda" {
    rule = aws_cloudwatch_event_rule.lambda_schedule.name
    target_id = var.cloudwatch_event_rule_schedule_xray_lambda_target_id
    arn = aws_lambda_function.gus_xray_traces.arn
}


resource "aws_lambda_permission" "allow_events_bridge_to_run_lambda" {
    statement_id = "AllowExecutionFromCloudWatch"
    action = "lambda:InvokeFunction"
    function_name = aws_lambda_function.gus_xray_traces.function_name
    principal = "events.amazonaws.com"
}