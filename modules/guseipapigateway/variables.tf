variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Choose your region"
  type        = string
}

variable "development_usage_plan_name" {
  description = ""
  type        = string
}

variable "cognito_user_pool_id" {
  description = ""
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}



variable "api_gateway_certificate_acm_certificate_arn" {
  type = string
}

variable "apphero_consumer_api_key" {
  type = string
}

variable "ibat_el_consumer_api_key" {
  type = string
}

variable "lim_consumer_api_key" {
  type = string
}

variable "hzu_consumer_api_key" {
  type = string
}

variable "unfc_consumer_api_key" {
  type = string
}
variable "ue_consumer_api_key"{
  type = string
}
variable "gus_eip_sf_api_key"{
    type = string
}

variable "oap_integration_consumer_api_key" {
  type = string
}

variable "gus_universal_api_key" {
  type = string
}

variable "r3_consumer_api_key" {
  type = string
}

variable "eip_gateway_certificate_acm_certificate_arn" {
  type = string
}

variable "eip_gateway_custom_domain" {
  type = string
}

variable "ibat_pipeline_consumer_api_key" {
  type = string
}

variable "ibat_pipeline_cognito_user_pool_id" {
  type = string
}
