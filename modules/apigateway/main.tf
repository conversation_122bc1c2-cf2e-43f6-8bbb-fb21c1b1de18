provider "aws" {
  region = var.region
}

resource "aws_api_gateway_rest_api" "api" {
  name        = "gusmiddleware-services-${var.environment}"
  description = "API"
  tags = {
    Environment = var.environment_tag
    Project     = "EIP"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_authorizer" "cognito_authorizer" {
  name                   = "apphero_cognito_authorizer"
  type                   = "COGNITO_USER_POOLS"
  rest_api_id            = aws_api_gateway_rest_api.api.id
  identity_source        = "method.request.header.Authorization"
  provider_arns          = [
    "arn:aws:cognito-idp:${var.region}:${var.accountId}:userpool/${var.cognito_user_pool_id}"
  ]
}

resource "aws_api_gateway_method" "method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_rest_api.api.root_resource_id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_rest_api.api.root_resource_id
  http_method             = aws_api_gateway_method.method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = aws_api_gateway_method_response.method_response.status_code
  depends_on = [
    aws_api_gateway_integration.integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }

  response_templates = {
      "application/json" = jsonencode({
        statusCode = 200
      })
    }
}

#STUDENT FILES
resource "aws_api_gateway_resource" "student_file_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "studentfile"
}

resource "aws_api_gateway_method" "student_file_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_file_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_file_resource.id
  http_method             = aws_api_gateway_method.student_file_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_file_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_file_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_file_resource.id
  http_method             = aws_api_gateway_method.student_file_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_file_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_resource.id
  http_method = aws_api_gateway_method.student_file_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_file_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_resource.id
  http_method = aws_api_gateway_method.student_file_options_method.http_method
  status_code = aws_api_gateway_method_response.student_file_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_file_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_resource" "student_file_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_file_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_file_proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_file_proxy_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method             = aws_api_gateway_method.student_file_proxy_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_file_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_file_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method             = aws_api_gateway_method.student_file_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_file_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method = aws_api_gateway_method.student_file_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_file_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_file_proxy_resource.id
  http_method = aws_api_gateway_method.student_file_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.student_file_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_file_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#STUDENT DETAIL
resource "aws_api_gateway_resource" "student_detail_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "studentdetail"
}

resource "aws_api_gateway_method" "student_detail_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_detail_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_resource.id
  http_method             = aws_api_gateway_method.student_detail_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_detail_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_detail_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_resource.id
  http_method             = aws_api_gateway_method.student_detail_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_detail_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_resource.id
  http_method = aws_api_gateway_method.student_detail_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_detail_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_resource.id
  http_method = aws_api_gateway_method.student_detail_options_method.http_method
  status_code = aws_api_gateway_method_response.student_detail_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_detail_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_resource" "student_detail_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_detail_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_detail_proxy_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_detail_proxy_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method             = aws_api_gateway_method.student_detail_proxy_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_detail_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_detail_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method             = aws_api_gateway_method.student_detail_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_detail_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_detail_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.student_detail_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_detail_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#STUDENT DETAIL LOOKUP

resource "aws_api_gateway_resource" "student_detail_lookup_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "lookup"
}


resource "aws_api_gateway_resource" "student_detail_proxy_lookup_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_detail_lookup_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_detail_proxy_lookup_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_detail_proxy_lookup_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method             = aws_api_gateway_method.student_detail_proxy_lookup_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-detail-backend-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_detail_proxy_lookup_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_detail_proxy_lookup_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method             = aws_api_gateway_method.student_detail_proxy_lookup_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "student_detail_proxy_lookup_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_lookup_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "student_detail_proxy_lookup_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_detail_proxy_lookup_resource.id
  http_method = aws_api_gateway_method.student_detail_proxy_lookup_options_method.http_method
  status_code = aws_api_gateway_method_response.student_detail_proxy_lookup_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_detail_proxy_lookup_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#APP HERO
resource "aws_api_gateway_resource" "apphero_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "apphero"
}

resource "aws_api_gateway_resource" "apphero_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.apphero_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "apphero_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.apphero_proxy_resource.id
  http_method   = "ANY"
  authorization = "COGNITO_USER_POOLS"

  request_parameters = {
    "method.request.path.proxy" = true
  }
  authorizer_id = aws_api_gateway_authorizer.cognito_authorizer.id
  authorization_scopes = ["aws.cognito.signin.user.admin", "email", "openid", "profile"]
}

resource "aws_api_gateway_integration" "apphero_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.apphero_proxy_resource.id
  http_method             = aws_api_gateway_method.apphero_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:apphero-backend-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "apphero_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.apphero_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "apphero_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.apphero_proxy_resource.id
  http_method             = aws_api_gateway_method.apphero_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "apphero_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.apphero_proxy_resource.id
  http_method = aws_api_gateway_method.apphero_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "apphero_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.apphero_proxy_resource.id
  http_method = aws_api_gateway_method.apphero_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.apphero_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.apphero_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#eip student details api
resource "aws_api_gateway_resource" "eip_studentdetail_root_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "profile"
}

resource "aws_api_gateway_resource" "eip_studentdetail_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.eip_studentdetail_root_resource.id
  path_part   = "getstudentdetail"
}

resource "aws_api_gateway_resource" "eip_studentdetail_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.eip_studentdetail_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "eip_studentdetail_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "eip_studentdetail_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method             = aws_api_gateway_method.eip_studentdetail_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "eip_studentdetail_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "eip_studentdetail_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method             = aws_api_gateway_method.eip_studentdetail_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "eip_studentdetail_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method = aws_api_gateway_method.eip_studentdetail_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "eip_studentdetail_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.eip_studentdetail_proxy_resource.id
  http_method = aws_api_gateway_method.eip_studentdetail_options_method.http_method
  status_code = aws_api_gateway_method_response.eip_studentdetail_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.eip_studentdetail_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#apphero country look up
resource "aws_api_gateway_resource" "apphero_lookup_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.apphero_resource.id
  path_part   = "lookup"
}

resource "aws_api_gateway_resource" "apphero_country_lookup_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.apphero_lookup_resource.id
  path_part   = "country"
}

resource "aws_api_gateway_method" "apphero_country_lookup_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.apphero_country_lookup_resource.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "apphero_country_lookup_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.apphero_country_lookup_resource.id
  http_method             = aws_api_gateway_method.apphero_country_lookup_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:apphero-backend-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "apphero_country_lookup_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.apphero_country_lookup_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "apphero_country_lookup_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.apphero_country_lookup_resource.id
  http_method             = aws_api_gateway_method.apphero_country_lookup_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "apphero_country_lookup_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.apphero_country_lookup_resource.id
  http_method = aws_api_gateway_method.apphero_country_lookup_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "apphero_country_lookup_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.apphero_country_lookup_resource.id
  http_method = aws_api_gateway_method.apphero_country_lookup_options_method.http_method
  status_code = aws_api_gateway_method_response.apphero_country_lookup_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.apphero_country_lookup_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

#GUS APPLICATION 
resource "aws_api_gateway_resource" "application_gus_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "application"
}

resource "aws_api_gateway_resource" "application_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.application_gus_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "application_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.application_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"

  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_integration" "application_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.application_proxy_resource.id
  http_method             = aws_api_gateway_method.application_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-oaf-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "application_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.application_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "application_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.application_proxy_resource.id
  http_method             = aws_api_gateway_method.application_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "application_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.application_proxy_resource.id
  http_method = aws_api_gateway_method.application_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "application_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.application_proxy_resource.id
  http_method = aws_api_gateway_method.application_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.application_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.application_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}


#GUS SALESFORCE 
resource "aws_api_gateway_resource" "gus_salesforce_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "salesforce"
}

resource "aws_api_gateway_resource" "gus_salesforce_api_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_salesforce_resource.id
  path_part   = "api"
}

resource "aws_api_gateway_method" "gus_salesforce_api_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_api_resource.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_salesforce_api_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_api_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_api_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

#GUS Salesforce API GET Proxy
resource "aws_api_gateway_resource" "gus_salesforce_api_get_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_salesforce_api_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "gus_salesforce_api_get_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_integration" "gus_salesforce_api_get_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_api_get_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-oaf-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_salesforce_api_get_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_salesforce_api_get_proxy_options_integration" {
  depends_on = [
    aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method
  ]
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_salesforce_api_get_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_salesforce_api_get_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_api_get_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_salesforce_api_get_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_salesforce_api_get_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
      "application/json" = jsonencode({
        statusCode = 200
      })
    }
}

#GUS Salesforce Proxy
resource "aws_api_gateway_resource" "gus_salesforce_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.gus_salesforce_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "gus_salesforce_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "gus_salesforce_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:gus-eip-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_salesforce_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_salesforce_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_salesforce_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_salesforce_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_salesforce_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_salesforce_proxy_resource.id
  http_method = aws_api_gateway_method.gus_salesforce_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_salesforce_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_salesforce_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
      "application/json" = jsonencode({
        statusCode = 200
      })
    }
}

#Gus Proxy
resource "aws_api_gateway_resource" "gus_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "gus_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_integration" "gus_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-oaf-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_proxy_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_proxy_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_proxy_resource.id
  http_method = aws_api_gateway_method.gus_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_proxy_resource.id
  http_method = aws_api_gateway_method.gus_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_proxy_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
      "application/json" = jsonencode({
        statusCode = 200
      })
    }
}

resource "aws_api_gateway_resource" "gus_submit_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "submit"
}

resource "aws_api_gateway_method" "gus_submit_get_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_submit_resource.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_submit_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_submit_resource.id
  http_method             = aws_api_gateway_method.gus_submit_get_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-pdf-generation-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_submit_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.gus_submit_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "gus_submit_options_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.gus_submit_resource.id
  http_method             = aws_api_gateway_method.gus_submit_options_method.http_method
  type                    = "MOCK"
  passthrough_behavior    = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_submit_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_submit_resource.id
  http_method = aws_api_gateway_method.gus_submit_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_submit_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.gus_submit_resource.id
  http_method = aws_api_gateway_method.gus_submit_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_submit_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_submit_options_integration
  ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin"  : "'*'"
    }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#gateway response
resource "aws_api_gateway_gateway_response" "access_denied_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "ACCESS_DENIED"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "unauthorized_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "UNAUTHORIZED"
  status_code  = 401
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "waf_filtered_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "WAF_FILTERED"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "request_too_large_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "REQUEST_TOO_LARGE"
  status_code  = 413
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_apikey_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "INVALID_API_KEY"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_4xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "DEFAULT_4XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "quota_exceeded_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "QUOTA_EXCEEDED"
  status_code  = 429
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "throttled_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "THROTTLED"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}



resource "aws_api_gateway_gateway_response" "bad_request_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "BAD_REQUEST_BODY"
  status_code  = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_signature_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "INVALID_SIGNATURE"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "missing_athentication_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "MISSING_AUTHENTICATION_TOKEN"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "bad_request_parameter_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "BAD_REQUEST_PARAMETERS"
  status_code  = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_5xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "DEFAULT_5XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "expired_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "EXPIRED_TOKEN"
  status_code  = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "resource_not_found_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id 
  response_type = "RESOURCE_NOT_FOUND"
  status_code  = 404
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin" = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}
resource "null_resource" "delete_stage" {
  provisioner "local-exec" {
    command = "echo 'Hello, Terraform!'"
  }
}
resource "aws_api_gateway_deployment" "gus_deployment" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  variables = {
    deployed_at = timestamp()
  }
  triggers = {
    redeployment = sha1(jsonencode(aws_api_gateway_rest_api.api.body))
  }
  lifecycle {
    create_before_destroy = true
  }
  depends_on    = [
    aws_api_gateway_integration.student_detail_proxy_lookup_integration,
    aws_api_gateway_integration.apphero_proxy_any_integration,
    aws_api_gateway_integration.apphero_country_lookup_integration,
    aws_api_gateway_integration.application_proxy_any_integration,
    aws_api_gateway_integration.gus_salesforce_api_integration,
    aws_api_gateway_integration.gus_salesforce_api_get_proxy_any_integration,
    aws_api_gateway_integration.gus_salesforce_proxy_any_integration,
    aws_api_gateway_integration.gus_proxy_any_integration,
    aws_api_gateway_integration.gus_submit_integration,
    aws_api_gateway_integration.integration,
    aws_api_gateway_integration.apphero_proxy_options_integration,
    aws_api_gateway_integration.apphero_country_lookup_options_integration,
    aws_api_gateway_integration.application_proxy_options_integration,
    aws_api_gateway_integration.gus_salesforce_api_get_proxy_options_integration,
    aws_api_gateway_integration.gus_salesforce_proxy_options_integration,
    aws_api_gateway_integration.gus_proxy_options_integration,
    aws_api_gateway_integration.gus_submit_options_integration,
    aws_api_gateway_method.method,
    aws_api_gateway_method.student_detail_proxy_lookup_method,
    aws_api_gateway_method.student_detail_proxy_lookup_options_method,
    aws_api_gateway_integration.student_detail_proxy_lookup_options_integration,
    aws_api_gateway_method.apphero_proxy_any_method,
    aws_api_gateway_method.apphero_proxy_options_method,
    aws_api_gateway_method.apphero_country_lookup_method,
    aws_api_gateway_method.apphero_country_lookup_options_method,
    aws_api_gateway_method.application_proxy_any_method,
    aws_api_gateway_method.application_proxy_options_method,
    aws_api_gateway_method.gus_salesforce_api_method,
    aws_api_gateway_method.gus_salesforce_api_get_proxy_any_method,
    aws_api_gateway_method.gus_salesforce_api_get_proxy_options_method,
    aws_api_gateway_method.gus_salesforce_proxy_any_method,
    aws_api_gateway_method.gus_salesforce_proxy_options_method,
    aws_api_gateway_method.gus_proxy_any_method,
    aws_api_gateway_method.gus_proxy_options_method,
    aws_api_gateway_method.gus_submit_get_method,
    aws_api_gateway_method.gus_submit_options_method,
    aws_api_gateway_method.student_detail_proxy_method,
    aws_api_gateway_integration.student_detail_proxy_integration,
    aws_api_gateway_method.student_detail_proxy_options_method,
    aws_api_gateway_integration.student_detail_proxy_options_integration,
    aws_api_gateway_method_response.method_response,
    aws_api_gateway_method_response.student_detail_options_method_response,
    aws_api_gateway_method_response.student_detail_proxy_options_method_response,
    aws_api_gateway_method_response.student_detail_proxy_lookup_options_method_response,
    aws_api_gateway_method_response.apphero_proxy_options_method_response,
    aws_api_gateway_method_response.apphero_country_lookup_options_method_response,
    aws_api_gateway_method_response.application_proxy_options_method_response,
    aws_api_gateway_method_response.gus_salesforce_api_get_proxy_options_method_response,
    aws_api_gateway_method_response.gus_salesforce_proxy_options_method_response,
    aws_api_gateway_method_response.gus_proxy_options_method_response,
    aws_api_gateway_method_response.gus_submit_options_method_response,
    aws_api_gateway_integration_response.integration_response,
    aws_api_gateway_integration_response.student_detail_options_integration_response,
    aws_api_gateway_integration_response.student_detail_proxy_options_integration_response,
    aws_api_gateway_integration_response.student_detail_proxy_lookup_options_integration_response,
    aws_api_gateway_integration_response.apphero_proxy_options_integration_response,
    aws_api_gateway_integration_response.apphero_country_lookup_options_integration_response,
    aws_api_gateway_integration_response.application_proxy_options_integration_response,
    aws_api_gateway_integration_response.gus_salesforce_api_get_proxy_options_integration_response,
    aws_api_gateway_integration_response.gus_proxy_options_integration_response,
    aws_api_gateway_integration_response.gus_salesforce_proxy_options_integration_response,
    aws_api_gateway_integration_response.gus_submit_options_integration_response,
    aws_api_gateway_method.student_file_method,
    aws_api_gateway_integration.student_file_integration,
    aws_api_gateway_method.student_file_options_method,
    aws_api_gateway_integration.student_file_options_integration,
    aws_api_gateway_method.student_file_proxy_method,
    aws_api_gateway_integration.student_file_proxy_integration,
    aws_api_gateway_method.student_file_proxy_options_method,
    aws_api_gateway_integration.student_file_proxy_options_integration,
    aws_api_gateway_method.eip_studentdetail_any_method,
    aws_api_gateway_integration.eip_studentdetail_integration,
    aws_api_gateway_method.eip_studentdetail_options_method,
    aws_api_gateway_integration.eip_studentdetail_options_integration,
    aws_api_gateway_integration.student_detail_integration,
    aws_api_gateway_method.student_detail_options_method,
    aws_api_gateway_integration.student_detail_options_integration
  ]
}
resource "aws_api_gateway_stage" "gus_deployment_stage" {
  deployment_id = aws_api_gateway_deployment.gus_deployment.id
  stage_name    = var.environment
  rest_api_id   = aws_api_gateway_rest_api.api.id
  xray_tracing_enabled = true
}

# resource "aws_api_gateway_domain_name" "gus_api_gateway_domain" {
#   domain_name = var.gus_api_gateway_custom_domain
#   certificate_arn = var.api_gateway_certificate_acm_certificate_arn
#   endpoint_configuration {
#     types = ["EDGE"]
#   }
# }

# resource "aws_api_gateway_base_path_mapping" "gus_api_gateway_mapping" {
#   api_id   = aws_api_gateway_rest_api.api.id
#   domain_name = aws_api_gateway_domain_name.gus_api_gateway_domain.id
#   stage_name = aws_api_gateway_stage.gus_deployment_stage.stage_name
# }
resource "aws_api_gateway_usage_plan" "development" {
  name         = "r3oaf"
  description  = "my description"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.gus_deployment_stage.stage_name
  }
}
resource "aws_api_gateway_usage_plan" "appHeroUsagePlan" {
  name         = "appHero"
  description  = "my description"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.gus_deployment_stage.stage_name
  }
}
resource "aws_api_gateway_usage_plan" "OAPIntegrationUsagePlan" {
  name         = "OAPIntegration"
  description  = "External OAP Integraiton Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.gus_deployment_stage.stage_name
  }
}
resource "aws_api_gateway_api_key" "apphero_api_key" {
  name = "APPHERO-API-KEY"
  value = "xphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNY"
}

resource "aws_api_gateway_api_key" "oap_integration_api_key" {
  name = "OAP-INTEGRATION-API-KEY"
}

resource "aws_api_gateway_api_key" "development_gateway_api_key" {
  name = "R3OAF-API-KEY"
}

resource "aws_api_gateway_usage_plan_key" "apphero" {
  key_id        = aws_api_gateway_api_key.apphero_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.appHeroUsagePlan.id
}
resource "aws_api_gateway_usage_plan_key" "main" {
  key_id        = aws_api_gateway_api_key.development_gateway_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.development.id
}
resource "aws_api_gateway_usage_plan_key" "oap" {
  key_id        = aws_api_gateway_api_key.oap_integration_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.OAPIntegrationUsagePlan.id
}
output "invoke_url" {
  value = aws_api_gateway_deployment.gus_deployment.invoke_url
}