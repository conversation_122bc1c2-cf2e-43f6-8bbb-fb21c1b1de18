provider "aws" {
  region = var.region
}

# Create ecr repository
/*resource "aws_ecr_repository" "salesforce_cdc" {
  name = "salesforce-cdc-${var.environment}"
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_security_group" "loadbalancer_sg" {
  name        = "salesforce-cdc-${var.environment}"
  description = "loadbalancer sg for salesforce-cdc"
  vpc_id      = var.aws_vpc[var.environment]
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
    # Add more tags as needed
  }

  # Define ingress rules (inbound traffic)
  ingress {
    from_port   = 3001
    to_port     = 3001
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  # Allow HTTP access from anywhere
  }

  # Define egress rules (outbound traffic)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"  # Allow all protocols
    cidr_blocks = ["0.0.0.0/0"]  # Allow all outbound traffic
  }
}

resource "aws_security_group" "salesforce_ecs_sg" {
  name        = "salesforce-ecs-${var.environment}-sg"
  description = "Application sg for salesforce-cdc"
  vpc_id      = var.aws_vpc[var.environment]
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
    # Add more tags as needed
  }

  # Define egress rules (outbound traffic)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"  # Allow all protocols
    cidr_blocks = ["0.0.0.0/0"]  # Allow all outbound traffic
  }
}

resource "aws_security_group_rule" "allow_traffic" {
  type              = "ingress"
  from_port         = 3001
  to_port           = 3001
  protocol          = "tcp"
  security_group_id = aws_security_group.salesforce_ecs_sg.id
  source_security_group_id = aws_security_group.loadbalancer_sg.id
}
resource "aws_ecr_lifecycle_policy" "salesforce_cdc_lifecycle_policy" {
  repository = aws_ecr_repository.salesforce_cdc.name

  policy = jsonencode({
    rules = [
      {
        rulePriority    = 1,
        description     = "in prod 10 images and in dev 3 images ",
        selection       = {
          tagStatus = "any",
          countType = "imageCountMoreThan",
          countNumber = var.environment == "prod" ? 10 : 3  # Adjust retention period as needed
        },
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# IAM Role for ECS Task
resource "aws_iam_role" "salesforce_cdc" {
  name = "salesforce-cdc-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = {
          Service = ["ecs-tasks.amazonaws.com"]
        }
        Action    = "sts:AssumeRole"
      }
          ]
  })
}

# IAM Policy for ECS Task
resource "aws_iam_policy" "salesforce_cdc" {
  name = "salesforce-cdc-policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        # allow scheduler to execute the task
        Effect = "Allow",
        Action = [
          "ecs:RunTask"
        ]
        # trim :<revision> from arn, to point at the whole task definition and not just one revision
        Resource = [trimsuffix(aws_ecs_task_definition.task.arn, ":${aws_ecs_task_definition.task.revision}")]
      },
      { # allow salesforce-cdc to set the IAM roles of your task
        Effect = "Allow",
        Action = [
          "ecr:*",
          "logs:*",
          "ecs:*"
        ]
        Resource = ["*"]
      },
      ]
  })
}

resource "aws_iam_role_policy_attachment" "salesforce_policy_attachment" {
  role       = aws_iam_role.salesforce_cdc.name
  policy_arn = aws_iam_policy.salesforce_cdc.arn
}

# Use local variables to reference the repository URI
locals {
  ecr_repo_uri = aws_ecr_repository.salesforce_cdc.repository_url
  image_with_tag = "${local.ecr_repo_uri}:latest"
}

resource "aws_ecs_task_definition" "task" {
  requires_compatibilities = ["FARGATE"]
  cpu                      = 256
  memory                   = 512
  skip_destroy             = true
  network_mode             = "awsvpc"
  # role that allows ECS to spin up your task, for example needs permission to ECR to get container image
  execution_role_arn = aws_iam_role.salesforce_cdc.arn
  # role that your workload gets to access AWS APIs
  task_role_arn      = aws_iam_role.salesforce_cdc.arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "X86_64"
  }

  family = "salesforce-cdc-${var.environment}"
  container_definitions = jsonencode([
    {
      name         = "salesforce-cdc-${var.environment}"
      image        = local.image_with_tag 
      cpu          = 256
      memory       = 512
      essential    = true
      requires_compatibilities = ["FARGATE"]
      network_mode             = "awsvpc"
      portMappings = [
        {
          containerPort = 3001
          hostPort      = 3001
          protocol      = "tcp"
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.log_group.name
          awslogs-region        = "${var.region}"
          awslogs-stream-prefix = "salesforce-cdc"
        }
      }
    }
  ])
}

resource "aws_cloudwatch_log_group" "log_group" {
  name              = "salesforce-cdc-${var.environment}"  # Name of the log group
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed  

}

resource "aws_ecs_service" "salesforce_cdc_service" {
  name            = "salesforce-cdc-${var.environment}"
  cluster         = "lim-oap-${var.environment}"
  task_definition = aws_ecs_task_definition.task.arn
  desired_count   = 0
  launch_type     = "FARGATE"
  load_balancer {
    target_group_arn = aws_lb_target_group.salesforce_cdc_tg.arn
    container_name   = "salesforce-cdc-${var.environment}"
    container_port   = 3001
  }
  network_configuration {
    subnets = var.private_subnet_id[var.environment]
    security_groups = [aws_security_group.salesforce_ecs_sg.id]
    assign_public_ip = false
  }
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

//Create application loadbalancer 
resource "aws_lb" "salesforce_cdc_alb" {
  name               = "salesforce-cdc-alb-${var.environment}"
  internal           = true
  load_balancer_type = "application"
  # Security group for the ALB (you can create a separate resource for security group if needed)
  security_groups    = [aws_security_group.loadbalancer_sg.id]
  
  # Subnets where ALB should be deployed
  subnets = var.private_subnet_id[var.environment]
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

// Create TargetGroup
resource "aws_lb_target_group" "salesforce_cdc_tg" {
  name     = "salesforce-cdc-${var.environment}"
  port     = 80
  protocol = "HTTP"
  target_type = "ip"
  
  # VPC where target group should be created
  vpc_id   = var.aws_vpc[var.environment]  # Modify with your VPC ID

  # Health check configuration
  health_check {
    path                = "/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    interval            = 30
    timeout             = 10
    healthy_threshold   = 2
    unhealthy_threshold = 2
  }
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}


resource "aws_lb_listener" "example_alb_listener" {
  load_balancer_arn = aws_lb.salesforce_cdc_alb.arn
  port              = 3001
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.salesforce_cdc_tg.arn
  }
  depends_on = [aws_lb_target_group.salesforce_cdc_tg]
}*/


