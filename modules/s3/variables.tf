
variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "The environment (e.g., dev, prod)"
  type        = string
}

variable "gus_xray_traces_bucket_name" {
  type = string
}

variable "gus_athena_query_output_bucket_name" {
  type = string
}

variable "r3_oaf_frontend_bucket_name" {
  type = string
}

variable "r3_oaf_backend_bucket_name" {
  type = string
}

variable "gus_middleware_service_bucket_name" {
  type = string
}

variable "r3_pdf_generator_bucket_name" {
  type = string
}

variable "gus_eip_analytics_bucket_name" {
  type = string
  
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}