variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "The environment (e.g., dev, prod)"
  type        = string
}

variable "xray_traces_catalog_database_name" {
  type        = string
}


variable "xray_traces_catalog_table_name" {
  type        = string
}

variable "aws_glue_catalog_table_type" {
  type        = string
}

variable "storage_descriptor_location" {
  type        = string
}

variable "storage_descriptor_input_format" {
  type        = string
}

variable "storage_descriptor_output_format" {
  type        = string
}

variable "ser_de_info_name" {
  type        = string
}

variable "ser_de_info_serialization_library" {
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}