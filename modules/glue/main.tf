provider "aws" {
  region = var.region
}

resource "aws_glue_catalog_database" "xray_traces_catalog_database" {
  name = "${var.xray_traces_catalog_database_name}-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_glue_catalog_table" "aws_glue_catalog_table" {
  name          = "${var.xray_traces_catalog_table_name}-${var.environment}"
  database_name = aws_glue_catalog_database.xray_traces_catalog_database.name
  table_type = var.aws_glue_catalog_table_type

  parameters = {
    EXTERNAL              = "TRUE"
    "parquet.compression" = "SNAPPY"
  }

  storage_descriptor {
    location      = var.storage_descriptor_location
    input_format  = var.storage_descriptor_input_format
    output_format = var.storage_descriptor_output_format

    ser_de_info {
      name                  = var.ser_de_info_name
      serialization_library = var.ser_de_info_serialization_library
      parameters = {
        "serialization.format" = 1
      }
    }

    columns {
    name = "id"
    type = "string"
  }

  columns {
    name = "duration"
    type = "double"
  }

  columns {
    name    = "responseTime"
    type    = "double"
  }

  columns {
    name    = "hasfault"
    type    = "boolean"
  }

  columns {
    name    = "haserror"
    type    = "boolean"
  }

  columns {
    name    = "hasthrottle"
    type    = "boolean"
  }

  columns {
    name    = "ispartial"
    type    = "boolean"
  }

  columns {
    name    = "http_httpurl"
    type    = "string"
  }
  columns {
    name    = "http_httpstatus"
    type    = "int"
  }
  columns {
    name    = "http_httpmethod"
    type    = "string"
  }
  columns {
    name    = "http_useragent"
    type    = "string"
  }
  columns {
    name    = "http_clientip"
    type    = "string"
  }
  columns {
    name    = "entrypoint_name"
    type    = "string"
  }
  columns {
    name    = "entrypoint_names"
    type    = "string"
  }
  columns {
    name    = "entrypoint_accountid"
    type    = "string"
  }
  columns {
    name    = "entrypoint_type"
    type    = "string"
  }
  columns {
    name    = "revision"
    type    = "string"
  }
  columns {
    name    = "consumer"
    type    = "string"
  }
  columns {
    name    = "matchedeventtime"
    type    = "string"
  }
  columns {
    name    = "annotations_api_key"
    type    = "string"
  }
  columns {
    name    = "epochtimestamp"
    type    = "bigint"
  }
  columns {
    name    = "day"
    type    = "string"
  }
  columns {
    name    = "month"
    type    = "string"
  }
  columns {
    name    = "year"
    type    = "int"
  }

  }
}
