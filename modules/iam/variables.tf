variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "The environment (e.g., dev, prod)"
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

variable "athena_access_accountId" {
  description = ""
  type        = string
}

variable "athena_access_role_name" {
  description = ""
  type        = string
}

variable "s3_role_arn" {
  description = ""
  type        = string
}

variable "dev_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "prod_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "prod_oap_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "dev_ecs_assumed_role_arn" {
  description = ""
  type        = string
}

variable "r3_oaf_frontend_pipeline_name" {
  description = ""
  type        = string
}
variable "r3_oaf_backend_pipeline_name" {
  description = ""
  type        = string
}
variable "gus_middleware_service_pipeline_name" {
  description = ""
  type        = string
}
variable "r3_pdf_generator_pipeline_name" {
  description = ""
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}

variable "s3_oap_cross_account_role_arn" {
  description = ""
  type        = string
}