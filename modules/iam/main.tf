provider "aws" {
  region = var.region
}
resource "aws_iam_role" "s3_cross_account_accessrole" {
  name = "s3CrossAccountAccessRole-${var.environment}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = [
            var.prod_lambda_assumed_role_arn,
            var.dev_lambda_assumed_role_arn,
            var.dev_ecs_assumed_role_arn,
            "arn:aws:iam::${var.athena_access_accountId}:root", var.prod_oap_lambda_assumed_role_arn,
            "arn:aws:sts::${var.accountId}:assumed-role/lambda-exec-role-${var.environment}/oap-notifications-${var.environment}",
            "arn:aws:iam::${var.accountId}:role/oap-lambda-exec-role-${var.environment}",
            "arn:aws:sts::************:assumed-role/apphero-lambda-exec-role-prod/apphero-backend-service-prod",
            "arn:aws:sts::************:assumed-role/apphero-lambda-exec-role-dev/apphero-backend-service-dev",
            "arn:aws:sts::${var.accountId}:assumed-role/gus-lambda-exec-role-${var.environment}/gus-eip-services-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/lambda-exec-role-${var.environment}/gus-eip-services-${var.environment}"
            ]
        },
      },
    ],
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "s3_cross_account_policy" {
  name        = "s3CrossAccountAccessPolicy-${var.environment}"
  description = "A policy that allows access to s3"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "s3_cross_access_policy_attachment" {
  policy_arn = aws_iam_policy.s3_cross_account_policy.arn
  role       = aws_iam_role.s3_cross_account_accessrole.name
}
resource "aws_iam_role" "athena_role" {
  name = "AthenaAccessRole-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${var.athena_access_accountId}:role/service-role/${var.athena_access_role_name}"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "athena_policy" {
  name        = "AthenaFullAccessPolicy-${var.environment}"
  description = "A policy that allows access to Athena"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "glue:*",
          "glue:BatchGetPartition",
          "cloudwatch:ListMetrics",
          "logs:DescribeLogGroups"
        ],
        Effect   = "Allow",
        Resource = "*"
      },
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "athena:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_role_policy_attachment" "athena_policy_attachment" {
  policy_arn = aws_iam_policy.athena_policy.arn
  role       = aws_iam_role.athena_role.name
}
#apphero_notification_role
resource "aws_iam_role" "apphero_graphQl_role" {
  name = "appsync-graphQL-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "appsync.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${var.accountId}:role/gus-lambda-exec-role-${var.environment}"
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "apphero_graphQl_policy" {
  name        = "apphero_graphQl_policy-${var.environment}"
  description = "apphero graphQl policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "appsync:GraphQL"
        ]
        Resource = "*"
        Effect   = "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "apphero_graphql_policy_attachment" {
  name       = "apphero_graphql_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.apphero_graphQl_policy.arn
  roles      = [aws_iam_role.apphero_graphQl_role.name]
}

#lambda_execution_role
resource "aws_iam_role" "lambda_exec_role" {
  name = "gus-lambda-exec-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = [
            var.s3_role_arn,
            var.s3_oap_cross_account_role_arn
          ]
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "ses.amazonaws.com"
        }
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "lambda_exec_policy" {
  name        = "gus_lambda_exec_policy-${var.environment}"
  description = "gus lambda exec policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "xray:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "apigateway:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "lambda:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "dynamodb:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "secretsmanager:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource : "*"
      },
      {
        Effect = "Allow",
        Action = [
          "ses:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "SNS:*",
          "sqs:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "sts:AssumeRole"
        ],
        Resource = "arn:aws:iam::${var.accountId}:role/appsync-graphQL-${var.environment}"
      },
       {
        Effect = "Allow",
        Action = [
          "ssm:*"
        ],
        Resource = "*"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "lambda_exec_policy_attachment" {
  name       = "lambda_exec_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.lambda_exec_policy.arn
  roles      = [aws_iam_role.lambda_exec_role.name]
}

#lambda_execution_role
resource "aws_iam_role" "lambda_execution_role" {
  name = "lambda-exec-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = [
            var.s3_role_arn,
            var.s3_oap_cross_account_role_arn
          ]
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "lambda_execution_policy" {
  name        = "lambda_exec_policy-${var.environment}"
  description = "lambda exec policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action   = "*"
        Resource = "*"
        Effect   = "Allow"
      }
      # {
      #   Action = [
      #     "logs:CreateLogGroup",
      #     "logs:CreateLogStream",
      #     "logs:PutLogEvents",
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "cloudwatch:DeleteAlarms",
      #     "cloudwatch:DescribeAlarmHistory",
      #     "cloudwatch:DescribeAlarms",
      #     "cloudwatch:DescribeAlarmsForMetric",
      #     "cloudwatch:GetMetricStatistics",
      #     "cloudwatch:ListMetrics",
      #     "cloudwatch:PutMetricAlarm",
      #     "cloudwatch:GetMetricData",
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "ec2:DescribeVpcs",
      #     "ec2:DescribeSubnets",
      #     "ec2:DescribeSecurityGroups",
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "cloudformation:CreateChangeSet",
      #     "cloudformation:DescribeChangeSet",
      #     "cloudformation:DescribeStackResource",
      #     "cloudformation:DescribeStacks",
      #     "cloudformation:ExecuteChangeSet"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "tag:GetResources"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "kms:DescribeKey",
      #     "kms:ListAliases",
      #     "kms:ListKeys",
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "events:*"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "iam:CreateServiceLinkedRole",
      #     "iam:GetRole",
      #     "iam:ListRoles",
      #     "iam:PassRole"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "xray:*"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "s3:*"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "lambda:*"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },
      # {
      #   Action = [
      #     "dynamodb:*"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # },      
      # {
      #   Action = [
      #     "secretsmanager:*"
      #   ]
      #   Resource = "*"
      #   Effect   = "Allow"
      # }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "lambda_execution_policy_attachment" {
  name       = "lambda_execution_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.lambda_execution_policy.arn
  roles      = [aws_iam_role.lambda_execution_role.name]
}

#codepipeline access role
resource "aws_iam_role" "codepipeline_access_role" {
  name = "gus-pipeline-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codepipeline.amazonaws.com"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

data "aws_iam_policy_document" "codepipeline_policy" {
  statement {
    effect = "Allow"

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetBucketVersioning",
      "s3:PutObjectAcl",
      "s3:PutObject",
    ]

    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["codestar-connections:UseConnection"]
    resources = ["*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "codecommit:CancelUploadArchive",
      "codecommit:GetBranch",
      "codecommit:GetCommit",
      "codecommit:GetRepository",
      "codecommit:GetUploadArchiveStatus",
      "codecommit:UploadArchive"
    ]
    resources = [
      "arn:aws:codecommit:${var.region}:${var.accountId}:r3-oaf-backend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-middleware-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-eip-infra",
      "arn:aws:codecommit:${var.region}:${var.accountId}:r3-pdf-generator",
      "arn:aws:codecommit:${var.region}:${var.accountId}:r3-oaf-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-apphero-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:apphero-backend-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-student-detail-oaf-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-student-detail-backend-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:apphero-sf-sync-service",
      "arn:aws:codecommit:${var.region}:${var.accountId}:gus-eip-analytics"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudwatch:*"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "codebuild:*"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudfront:GetDistribution",
      "cloudfront:GetInvalidation",
      "cloudfront:CreateInvalidation"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_role_policy" "codepipeline_policy" {
  name   = "codepipeline_policy_${var.environment}"
  role   = aws_iam_role.codepipeline_access_role.id
  policy = data.aws_iam_policy_document.codepipeline_policy.json
}


# codebuild access role
resource "aws_iam_role" "gus_codebuild_access" {
  name = "gus-codebuild-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "gus_codebuild_access_policy" {
  name = "gus-codebuild-access-policy-${var.environment}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Resource : [
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-middleware-service-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-middleware-service-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/${var.environment}-gus-middleware-service",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/${var.environment}-gus-middleware-service:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-pdf-generator-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-pdf-generator-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-backend:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-frontend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/r3-oaf-frontend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-apphero-frontend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-apphero-frontend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/apphero-backend-service-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-oaf-frontend-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-oaf-frontend-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-backend-service-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-student-detail-backend-service-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/gus-eip-analytics-${var.environment}",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/*"
        ],
        Action : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "s3:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "athena:CreateWorkGroup"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "logs:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "lambda:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "appsync:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "cloudfront:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "apigateway:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "cloudwatch:*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "codebuild:CreateReportGroup",
          "codebuild:CreateReport",
          "codebuild:UpdateReport",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds",
          "codebuild:BatchGetProjects",
          "codebuild:CreateProject",
          "codebuild:ListProjects",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StopBuild",
          "codebuild:RetryBuild"
        ],
        Resource : [
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-backend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-middleware-service-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/${var.environment}-gus-middleware-service",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-pdf-generation-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-backend",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-frontend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-frontend-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-apphero-frontend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-apphero-frontend-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/r3-oaf-backend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-backend-service-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-backend-service-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-student-detail-oaf-frontend-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-student-detail-oaf-frontend-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-sf-sync-service-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/apphero-sf-sync-service-${var.environment}",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-eip-analytics-${var.environment}:*",
          "arn:aws:codebuild:${var.region}:${var.accountId}:report-group/gus-eip-analytics-${var.environment}"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "dynamodb:*"
        ],
        Resource : [
          "arn:aws:dynamodb:${var.region}:${var.accountId}:table/*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "cloudformation:DescribeStacks",
          "cloudformation:DescribeStackResource",
          "cloudformation:ValidateTemplate",
          "cloudformation:DeleteChangeSet",
          "cloudformation:CreateChangeSet",
          "cloudformation:DescribeChangeSet",
          "cloudformation:ExecuteChangeSet",
          "cloudformation:DescribeStackEvents",
          "cloudformation:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "glue:CreateDatabase",
          "glue:BatchGetCrawlers",
          "glue:ListCrawlers",
          "glue:GetTables",
          "glue:GetDatabase",
          "glue:TagResource",
          "glue:UpdateDatabase",
          "glue:CreateTable",
          "glue:UpdateTable",
          "glue:GetTags",
          "glue:DeleteDatabase",
          "glue:DeleteTable",
          "glue:GetTable"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "iam:CreatePolicy",
          "iam:AttachRolePolicy",
          "iam:DeletePolicy",
          "iam:GetPolicy",
          "iam:ListPolicies",
          "iam:CreateRole",
          "iam:GetPolicy",
          "iam:GetRole",
          "iam:ListPolicies",
          "iam:ListRoles",
          "iam:UpdateRole",
          "iam:PutRolePolicy",
          "iam:GetRole",
          "iam:PassRole",
          "iam:ListRolePolicies",
          "iam:GetPolicyVersion",
          "iam:ListAttachedRolePolicies",
          "iam:ListPolicyVersions",
          "iam:ListInstanceProfilesForRole",
          "iam:DeleteRole",
          "iam:ListEntitiesForPolicy",
          "iam:GetRolePolicy",
          "iam:ListEntitiesForPolicy",
          "iam:DetachRolePolicy",
          "iam:DeleteRolePolicy",
          "iam:TagRole",
          "iam:CreateServiceLinkedRole"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "ecr:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "ssm:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "events:PutRule",
          "events:DescribeRule",
          "events:ListTagsForResource",
          "events:DeleteRule",
          "events:PutTargets",
          "events:ListTargetsByRule",
          "events:RemoveTargets"
        ],
        Resource : [
          "*"
        ]
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "gus_codebuild_access_policy_attachment" {
  name       = "gus_codebuild_access_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.gus_codebuild_access_policy.arn
  roles      = [aws_iam_role.gus_codebuild_access.name]
}

# codepipeline start role
resource "aws_iam_role" "gus_codePipeline_start_access" {
  name = "gus-codePipeline-start-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Principal : {
          Service : "events.amazonaws.com"
        },
        Action : "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "gus_codePipeline_start_policy" {
  name = "gus-codePipeline-start-access-policy-${var.environment}"

  policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Action : [
          "codepipeline:StartPipelineExecution"
        ],
        Resource : [
          "arn:aws:codepipeline:${var.region}:${var.accountId}:${var.r3_oaf_frontend_pipeline_name}-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:apphero-backend-service-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:${var.r3_oaf_backend_pipeline_name}-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:${var.gus_middleware_service_pipeline_name}-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:${var.r3_pdf_generator_pipeline_name}-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:gus-student-detail-backend-service-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:apphero-sf-sync-service-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:gus-eip-analytics-${var.environment}",
          "arn:aws:codepipeline:${var.region}:${var.accountId}:gus-eip-analytics-${var.environment}"
        ]
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "gus_codePipeline_start_access_policy_attachment" {
  name       = "gus_codePipeline_start_access_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.gus_codePipeline_start_policy.arn
  roles      = [aws_iam_role.gus_codePipeline_start_access.name]
}
