provider "aws" {
  region = var.region
}

# r3-oaf-frontend-project
resource "aws_cloudwatch_log_group" "r3-oaf-frontend" {
  name = "build-logs-r3-oaf-frontend-service-${var.environment}"

  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "r3-oaf-frontend-project" {
  name          = "${var.r3_oaf_frontend_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.r3_oaf_frontend_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/gus-codebuild-access-${var.environment}"

  source {
    type = var.r3_oaf_frontend_project_source_type
  }

  environment {
    compute_type                = var.r3_oaf_frontend_project_environment_compute_type
    image                       = var.r3_oaf_frontend_project_environment_image
    type                        = var.r3_oaf_frontend_project_environment_type
    image_pull_credentials_type = var.r3_oaf_frontend_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.r3-oaf-frontend.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.r3_oaf_frontend_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

//r3-oaf-backend
resource "aws_cloudwatch_log_group" "r3-oaf-backend-project" {
  name = "build-logs-r3-oaf-backend-project-${var.environment}"

  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "r3-oaf-backend-project" {
  name          = "${var.r3_oaf_backend_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.r3_oaf_backend_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/gus-codebuild-access-${var.environment}"

  source {
    type = var.r3_oaf_backend_project_source_type
  }

  environment {
    compute_type                = var.r3_oaf_backend_project_environment_compute_type
    image                       = var.r3_oaf_backend_project_environment_image
    type                        = var.r3_oaf_backend_project_environment_type
    image_pull_credentials_type = var.r3_oaf_backend_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.r3-oaf-backend-project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.r3_oaf_backend_project_artifact_type
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

//gus-middleware-service
resource "aws_cloudwatch_log_group" "gus-middleware-service-project" {
  name = "build-logs-gus-middleware-service-${var.environment}"

  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "gus-middleware-service-project" {
  name          = "${var.gus_middleware_service_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.gus_middleware_service_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/gus-codebuild-access-${var.environment}"

  source {
    type = var.gus_middleware_service_project_source_type
  }

  environment {
    compute_type                = var.gus_middleware_service_project_environment_compute_type
    image                       = var.gus_middleware_service_project_environment_image
    type                        = var.gus_middleware_service_project_environment_type
    image_pull_credentials_type = var.gus_middleware_service_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.gus-middleware-service-project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.gus_middleware_service_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

//r3-pdf-generator

resource "aws_cloudwatch_log_group" "r3-pdf-generator-project" {
  name = "build-logs-r3-pdf-generator-project-${var.environment}"

  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "r3-pdf-generator-project" {
  name          = "${var.r3_pdf_generator_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.r3_pdf_generator_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/gus-codebuild-access-${var.environment}"

  source {
    type = var.r3_pdf_generator_project_source_type
  }

  environment {
    compute_type                = var.r3_pdf_generator_project_environment_compute_type
    image                       = var.r3_pdf_generator_project_environment_image
    type                        = var.r3_pdf_generator_project_environment_type
    image_pull_credentials_type = var.r3_pdf_generator_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.r3-pdf-generator-project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.r3_pdf_generator_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "R3OAF"
    TEAM        = "EIP Development Team"
  }
}

// gus-eip-analytics
resource "aws_cloudwatch_log_group" "gus-eip-analytics-project" {
  name = "build-logs-gus-eip-analytics-project-${var.environment}"
  # Optionally, configure log group properties
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_codebuild_project" "gus-eip-analytics-project" {
  name          = "${var.gus_eip_analytics_project_name}-${var.environment}"
  description   = ""
  build_timeout = var.gus_eip_analytics_project_build_timeout
  service_role  = "arn:aws:iam::${var.accountId}:role/gus-codebuild-access-${var.environment}"

  source {
    type = var.gus_eip_analytics_project_source_type
  }

  environment {
    compute_type                = var.gus_eip_analytics_project_environment_compute_type
    image                       = var.gus_eip_analytics_project_environment_image
    type                        = var.gus_eip_analytics_project_environment_type
    image_pull_credentials_type = var.gus_eip_analytics_project_environment_image_pull_credentials_type
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }

    environment_variable {
      name  = "region"
      value = var.region
    }

    environment_variable {
      name  = "restApiId"
      value = var.restApiId
    }

    environment_variable {
      name  = "restApiRootResourceId"
      value = var.restApiRootResourceId
    }

    environment_variable {
      name  = "securityGroupId"
      value = var.securityGroupId
    }

    environment_variable {
      name  = "subnetId"
      value = var.subnetId
    }
  }

  logs_config {
    cloudwatch_logs {
      status            = "ENABLED"
      group_name        = aws_cloudwatch_log_group.gus-eip-analytics-project.name
      stream_name       = "{CODEBUILD_BUILD_ID}"
    }
  }

  artifacts {
    type = var.gus_eip_analytics_project_artifact_type
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
