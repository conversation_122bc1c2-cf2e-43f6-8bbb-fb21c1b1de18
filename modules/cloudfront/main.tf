provider "aws" {
  region = var.region
}
resource "aws_cloudfront_response_headers_policy" "custom_response_header_policy" {
  name = "r3-response-headers-policy-${var.environment}"

  cors_config {
    access_control_allow_credentials = false
    access_control_allow_headers {
      items = ["*"]
    }

    access_control_allow_methods {
      items = ["HEAD", "POST", "PATCH", "DELETE", "PUT", "GET", "OPTIONS"]
    }

    access_control_allow_origins {
      items = ["*"]
    }

    origin_override = true
  }
  security_headers_config {
    content_type_options {
      override = true
    }
    frame_options {
      frame_option = "SAMEORIGIN"
      override     = true
    }
    referrer_policy {
      referrer_policy = "origin-when-cross-origin"
      override        = true
    }
    xss_protection {
      mode_block = true
      protection = true
      override   = true
    }
    strict_transport_security {
      access_control_max_age_sec = "63072000"
      include_subdomains         = true
      preload                    = true
      override                   = true
    }
    content_security_policy {
      content_security_policy = var.s3_distribution_r3_content_security_policy
      override                = true
    }
  }
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  origin {
    domain_name = var.s3_distribution_domain_name
    origin_id   = var.s3_distribution_origin_id
    custom_origin_config {
      http_port              = var.s3_distribution_http_port
      https_port             = var.s3_distribution_https_port
      origin_protocol_policy = var.s3_distribution_origin_protocol_policy
      origin_ssl_protocols   = var.s3_distribution_origin_ssl_protocols
    }
  }

  enabled         = var.s3_distribution_enabled
  is_ipv6_enabled = var.s3_distribution_is_ipv6_enabled

  aliases = var.gus_alternative_domain

  default_cache_behavior {
    allowed_methods            = var.s3_distribution_default_cache_behavior_allowed_methods
    cached_methods             = var.s3_distribution_default_cache_behavior_cached_methods
    target_origin_id           = var.s3_distribution_default_cache_behavior_target_origin_id
    cache_policy_id            = var.s3_distribution_default_cache_behavior_cache_policy_id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.custom_response_header_policy.id

    viewer_protocol_policy = var.s3_distribution_viewer_protocol_policy
  }

  restrictions {
    geo_restriction {
      restriction_type = var.s3_distribution_geo_restriction_restriction_type
      locations        = []
    }
  }

  tags = {
    Environment = var.environment_tag
    Project     = "R3OAF"
    Team        = "EIP Development Team"
  }

  viewer_certificate {
    acm_certificate_arn            = var.s3_distribution_viewer_certificate_acm_certificate_arn
    cloudfront_default_certificate = var.s3_distribution_viewer_certificate_cloudfront_default_certificate
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.2_2021"
  }
}
