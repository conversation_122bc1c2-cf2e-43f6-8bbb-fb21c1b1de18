// r3-oaf-service api_gateway
resource "aws_api_gateway_rest_api" "r3_oaf_service_api" {
  name        = "r3-oaf-service-${var.environment}"
  description = "API Gatewy for r3-service"
  tags = {
    Environment = var.environment_tag
    Project     = "R3OAF"
    Team        = "EIP Development Team"
  }
}


resource "aws_api_gateway_method" "r3_oaf_service_method" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  http_method   = "OPTIONS"
  authorization = "NONE"
}



resource "aws_api_gateway_integration" "r3_oaf_service_integration" {
  rest_api_id          = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id          = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  http_method          = aws_api_gateway_method.r3_oaf_service_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "r3_oaf_service_method_response" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  http_method = aws_api_gateway_method.r3_oaf_service_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "r3_oaf_service_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  http_method = aws_api_gateway_method.r3_oaf_service_method.http_method
  status_code = aws_api_gateway_method_response.r3_oaf_service_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.r3_oaf_service_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }

  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}


#R3OAF APPLICATIONS


resource "aws_api_gateway_resource" "application_gus_resource1" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  parent_id   = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  path_part   = "application"
}

resource "aws_api_gateway_resource" "application_proxy_resource1" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  parent_id   = aws_api_gateway_resource.application_gus_resource1.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "application_proxy_any_method1" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_resource.application_proxy_resource1.id
  http_method   = "ANY"
  authorization = "NONE"

  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_integration" "application_proxy_any_integration1" {
  rest_api_id             = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id             = aws_api_gateway_resource.application_proxy_resource1.id
  http_method             = aws_api_gateway_method.application_proxy_any_method1.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-oaf-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "application_proxy_options_method1" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_resource.application_proxy_resource1.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "application_proxy_options_integration1" {
  rest_api_id          = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id          = aws_api_gateway_resource.application_proxy_resource1.id
  http_method          = aws_api_gateway_method.application_proxy_options_method1.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "application_proxy_options_method_response1" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_resource.application_proxy_resource1.id
  http_method = aws_api_gateway_method.application_proxy_options_method1.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "application_proxy_options_integration_response1" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_resource.application_proxy_resource1.id
  http_method = aws_api_gateway_method.application_proxy_options_method1.http_method
  status_code = aws_api_gateway_method_response.application_proxy_options_method_response1.status_code
  depends_on = [
    aws_api_gateway_integration.application_proxy_options_integration1
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#R3 proxy
resource "aws_api_gateway_resource" "gus_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  parent_id   = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "gus_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_resource.gus_proxy_resource.id
  http_method   = "ANY"
  authorization = "NONE"
  request_parameters = {
    "method.request.path.proxy" = true
  }
}

resource "aws_api_gateway_integration" "gus_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id             = aws_api_gateway_resource.gus_proxy_resource.id
  http_method             = aws_api_gateway_method.gus_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-oaf-services-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_resource.gus_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_proxy_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id          = aws_api_gateway_resource.gus_proxy_resource.id
  http_method          = aws_api_gateway_method.gus_proxy_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_resource.gus_proxy_resource.id
  http_method = aws_api_gateway_method.gus_proxy_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_resource.gus_proxy_resource.id
  http_method = aws_api_gateway_method.gus_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_proxy_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_resource" "gus_submit_resource" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  parent_id   = aws_api_gateway_rest_api.r3_oaf_service_api.root_resource_id
  path_part   = "submit"
}

resource "aws_api_gateway_method" "gus_submit_get_method" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_resource.gus_submit_resource.id
  http_method   = "GET"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "gus_submit_integration" {
  rest_api_id             = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id             = aws_api_gateway_resource.gus_submit_resource.id
  http_method             = aws_api_gateway_method.gus_submit_get_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:r3-pdf-generation-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "gus_submit_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id   = aws_api_gateway_resource.gus_submit_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "gus_submit_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id          = aws_api_gateway_resource.gus_submit_resource.id
  http_method          = aws_api_gateway_method.gus_submit_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "gus_submit_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_resource.gus_submit_resource.id
  http_method = aws_api_gateway_method.gus_submit_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "gus_submit_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  resource_id = aws_api_gateway_resource.gus_submit_resource.id
  http_method = aws_api_gateway_method.gus_submit_options_method.http_method
  status_code = aws_api_gateway_method_response.gus_submit_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.gus_submit_options_integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_deployment" "r3_oaf_deployment" {
  rest_api_id = aws_api_gateway_rest_api.r3_oaf_service_api.id
  variables = {
    deployed_at = timestamp()
  }
  triggers = {
    redeployment = sha1(jsonencode(aws_api_gateway_rest_api.r3_oaf_service_api.body))
  }
  lifecycle {
    create_before_destroy = true
  }
  depends_on = [
    aws_api_gateway_integration.r3_oaf_service_integration,
    aws_api_gateway_integration.application_proxy_options_integration1,
    aws_api_gateway_method.r3_oaf_service_method,
    aws_api_gateway_method.application_proxy_options_method1,
    aws_api_gateway_method_response.r3_oaf_service_method_response,
    aws_api_gateway_method_response.application_proxy_options_method_response1,
    aws_api_gateway_integration_response.r3_oaf_service_integration_response,
    aws_api_gateway_integration_response.application_proxy_options_integration_response1,
    aws_api_gateway_method.gus_proxy_any_method,
    aws_api_gateway_integration.gus_proxy_any_integration,
    aws_api_gateway_method.gus_proxy_options_method,
    aws_api_gateway_integration.gus_proxy_options_integration,
    aws_api_gateway_method.gus_submit_get_method,
    aws_api_gateway_integration.gus_submit_integration,
    aws_api_gateway_method.gus_submit_options_method,
    aws_api_gateway_integration.gus_submit_options_integration
  ]
}
resource "aws_api_gateway_stage" "r3_oaf_deployment_stage" {
  deployment_id        = aws_api_gateway_deployment.r3_oaf_deployment.id
  stage_name           = var.environment
  rest_api_id          = aws_api_gateway_rest_api.r3_oaf_service_api.id
  xray_tracing_enabled = true
  tags = {
    Environment = var.environment_tag
    Project     = "R3OAF"
    Team        = "EIP Development Team"
  }
}

resource "aws_api_gateway_domain_name" "r3_oaf_domain" {
  domain_name     = var.r3_oaf_gateway_custom_domain
  certificate_arn = var.api_gateway_certificate_acm_certificate_arn
  endpoint_configuration {
    types = ["EDGE"]
  }
}

resource "aws_api_gateway_base_path_mapping" "r3_oaf_gateway_mapping" {
  api_id      =   aws_api_gateway_rest_api.r3_oaf_service_api.id
  domain_name = aws_api_gateway_domain_name.r3_oaf_domain.id
  stage_name  = aws_api_gateway_stage.r3_oaf_deployment_stage.stage_name
}
output "invoke_url1" {
  value = aws_api_gateway_deployment.r3_oaf_deployment.invoke_url
}
