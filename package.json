{"name": "gus-middleware", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"format": "prettier --write .", "postinstall": "husky install", "lint": "tflint", "pre-commit": "pnpm run format", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "commit": "git-cz"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^17.6.7", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "handlebars": "^4.7.8", "husky": "^8.0.3", "plop": "^3.1.2", "prettier": "3.0.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "pnpm run format && pnpm run lint", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}