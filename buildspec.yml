version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 16
  pre_build:
    commands:
      - curl -LO "https://releases.hashicorp.com/terraform/1.5.1/terraform_1.5.1_linux_amd64.zip"
      - unzip "terraform_1.5.1_linux_amd64.zip"
      - mv terraform /usr/local/bin/
      - rm "terraform_1.5.1_linux_amd64.zip"
      - terraform --version
      - echo "Install Node dependencies for Lambda code"
      - cd modules/lambda/code
      - cd ../../..
      - cd layers/nodejs
      - npm install
      - cd ../../
      - cd layers2/nodejs
      - npm install
      - cd ../..
  build:
    commands:
      - echo "Build phase"
      - echo "Initialize Terraform"
      - terraform init
      - |
        if terraform workspace list | grep -q ${stage}; then
          echo "Workspace ${stage} exists, selecting it"
          terraform workspace select ${stage}
        else
          echo "Workspace ${stage} does not exist, creating it"
          terraform workspace new ${stage}
        fi
      - terraform state list
      - echo "Removing Athena Workgroup from Terraform state"
      - terraform state rm module.athena.aws_athena_workgroup.apphero_workgroup || echo "Workgroup already removed from state"
      - terraform state rm module.eip_apigateway.aws_api_gateway_integration.duolingo_validation_integration || echo "Already removed integration from state"
      - terraform state rm module.eip_apigateway.aws_api_gateway_integration.duolingo_validation_options_integration || echo "Already removed integration from state"
      - terraform state rm module.eip_apigateway.aws_api_gateway_method.duolingo_validation_post_method || echo "Already removed method from state"
      - terraform state rm module.eip_apigateway.aws_api_gateway_method.duolingo_validation_options_method || echo "Already removed method from state"
      - echo "Terraform validate"
      - terraform validate 
      - echo "Terraform plan"
      - terraform plan -lock=false -var-file=${stage}.tfvars
        # Add TF_LOG: DEBUG to your CodeBuild stage variables
      - echo "Terraform apply"
      - terraform apply -lock=false -var-file=${stage}.tfvars -auto-approve