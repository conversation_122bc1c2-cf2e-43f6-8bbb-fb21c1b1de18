# AWS Cognito Setup for IBAT Data Pipeline OAuth Client

## Overview

This document provides a comprehensive analysis of the AWS Cognito configuration for the "ibat-data-pipeline-oauth-client" in the GUS EIP infrastructure. The setup implements a machine-to-machine authentication system using OAuth 2.0 client credentials flow for secure API access.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Cognito User Pool Configuration](#cognito-user-pool-configuration)
- [OAuth Client Configuration](#oauth-client-configuration)
- [Resource Server and Scopes](#resource-server-and-scopes)
- [API Gateway Integration](#api-gateway-integration)
- [Permissions and Access Control](#permissions-and-access-control)
- [Environment Configuration](#environment-configuration)
- [Security Considerations](#security-considerations)
- [Usage and Integration](#usage-and-integration)

## Architecture Overview

The IBAT (presumably "Integrated Business Application Technology") data pipeline uses AWS Cognito for authentication and authorization. The system is designed with:

- **Multi-tenant User Pool**: Shared across multiple applications (IBAT, AppHero, EIP-GUS-SF)
- **Resource-based access control**: Each client has specific scopes and permissions
- **Environment separation**: Different configurations for development and production
- **API Gateway integration**: Cognito authorizers protect API endpoints

## Cognito User Pool Configuration

### User Pool Details

```terraform
resource "aws_cognito_user_pool" "cognito_ibd_authprovider" {
  name = "${var.environment}-ibd-authprovider"

  tags = {
    Environment = var.environment_tag
    PROJECT     = "APPHERO"
    TEAM        = "EIP Development Team"
  }
}
```

**Configuration:**
- **Name Pattern**: `{environment}-ibd-authprovider`
- **Development**: `dev-ibd-authprovider`
- **Production**: `prod-ibd-authprovider`
- **Project**: APPHERO
- **Team**: EIP Development Team

### Custom Domains

The User Pool is configured with custom domains for better branding and security:

```terraform
# Standard domain
resource "aws_cognito_user_pool_domain" "cognito_ipd_pipeline_domain" {
  domain       = "ibd-auth-provider-${var.environment}"
  user_pool_id = aws_cognito_user_pool.cognito_ibd_authprovider.id
}

# Custom domain with SSL certificate
resource "aws_cognito_user_pool_domain" "custom_ibd_domain" {
  domain       = var.cognito_ibd_custom_domain
  user_pool_id = aws_cognito_user_pool.cognito_ibd_authprovider.id
  certificate_arn = var.api_gateway_certificate_acm_certificate_arn
}
```

**Domain Configuration:**
- **Development**: `dev-authprovider.apphero.io`
- **Production**: `ibd-authprovider.apphero.io`
- **SSL Certificate**: ACM certificate for HTTPS

## OAuth Client Configuration

### IBAT Pipeline Client

```terraform
resource "aws_cognito_user_pool_client" "cognito_ibat_pipeline_client" {
  name                              = "ibat-data-pipeline-oauth-client-${var.environment}"
  user_pool_id                      = aws_cognito_user_pool.cognito_ibd_authprovider.id
  generate_secret                   = true
  allowed_oauth_flows               = ["client_credentials"]
  allowed_oauth_scopes              = ["x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read"]
  allowed_oauth_flows_user_pool_client = true

  depends_on = [
    aws_cognito_resource_server.cognito_ibd_authprovider_resource_server_apikey
  ]
}
```

**Client Configuration:**
- **Name**: `ibat-data-pipeline-oauth-client-{environment}`
- **Authentication Flow**: Client Credentials (machine-to-machine)
- **Client Secret**: Auto-generated and managed by Cognito
- **OAuth Flows**: Client credentials only
- **Scopes**: Read access to specific resource server

### Authentication Flow Details

**Client Credentials Flow:**
1. Client authenticates using client ID and secret
2. Cognito returns an access token
3. Access token is used to call protected APIs
4. Token includes specific scopes for authorization

## Resource Server and Scopes

### Resource Server Configuration

```terraform
resource "aws_cognito_resource_server" "cognito_ibd_authprovider_resource_server_apikey" {
  user_pool_id  = aws_cognito_user_pool.cognito_ibd_authprovider.id
  identifier    = "x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO"
  name          = "x-api-key"
  
  scope {
    scope_name        = "read"
    scope_description = "Read access to resources"
  }
}
```

**Resource Server Details:**
- **Identifier**: `x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO`
- **Name**: `x-api-key`
- **Available Scopes**:
  - `read`: Read access to resources
- **Full Scope**: `x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read`

## API Gateway Integration

### Authorizer Configuration

```terraform
resource "aws_api_gateway_authorizer" "ibat_pipeline_app_validation_authorizer" {
  name                   = "ibat-pipeline-authorizer-${var.environment}"
  type                   = "COGNITO_USER_POOLS"
  rest_api_id            = aws_api_gateway_rest_api.api.id
  identity_source        = "method.request.header.Authorization"
  provider_arns          = [
    "arn:aws:cognito-idp:${var.region}:${var.accountId}:userpool/${var.ibat_pipeline_cognito_user_pool_id}"
  ]
}
```

### Protected Endpoints

**IBAT Student Applications API:**
- **Path**: `/ibat/studentapplications`
- **Methods**: ANY (GET, POST, PUT, DELETE, etc.)
- **Authorization**: Cognito User Pools
- **Required Scopes**: `x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read`
- **Backend**: Lambda function `gus-eip-services-{environment}`

### API Gateway Structure

```
gus-eip-service-{environment}
├── /ibat
│   └── /studentapplications (ANY) → Lambda: gus-eip-services-{environment}
├── /apphero
│   └── /cobrand (ANY) → Lambda: gus-eip-services-{environment}
└── /eip
    └── /studentdetail (ANY) → Lambda: gus-eip-services-{environment}
```

## Permissions and Access Control

### Usage Plans and API Keys

```terraform
resource "aws_api_gateway_usage_plan" "IbatPipelineUsagePlan" {
  name         = "IbatPipeline"
  description  = "LIM Usage plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.api.id
    stage  = aws_api_gateway_stage.eip_deployment_stage.stage_name
  }
}

resource "aws_api_gateway_api_key" "ibat_pipeline_api_key" {
  name  = "IBAT-PIPELINE-API-KEY"
  value = var.ibat_pipeline_consumer_api_key
}
```

**Access Control Layers:**
1. **Cognito Authentication**: Valid OAuth token required
2. **Scope Authorization**: Token must include required scopes
3. **API Key**: Additional API key for usage plan management
4. **Usage Plan**: Rate limiting and quota management

## Environment Configuration

### Development Environment

```hcl
# dev.tfvars
ibat_pipeline_cognito_user_pool_id = "eu-west-1_VFcjqfvty"
cognito_ibd_custom_domain = "dev-authprovider.apphero.io"
```

### Production Environment

```hcl
# prod.tfvars
ibat_pipeline_cognito_user_pool_id = "eu-west-1_xKzncZTIv"
cognito_ibd_custom_domain = "ibd-authprovider.apphero.io"
```

**Environment Differences:**
- Separate User Pool IDs for isolation
- Different custom domains
- Environment-specific API keys and secrets

## Security Considerations

### Authentication Security

1. **Client Secret Management**:
   - Auto-generated by Cognito
   - Stored securely in AWS
   - Rotatable through Terraform

2. **Token Security**:
   - Short-lived access tokens
   - Scope-based authorization
   - No refresh tokens (client credentials flow)

3. **Network Security**:
   - HTTPS-only communication
   - Custom domains with SSL certificates
   - API Gateway integration for additional security layers

### Access Control

1. **Principle of Least Privilege**:
   - Each client has minimal required scopes
   - Resource-specific access control
   - Environment separation

2. **Multi-layered Security**:
   - Cognito authentication
   - Scope-based authorization
   - API key validation
   - Usage plan enforcement

## Usage and Integration

### Token Acquisition

```bash
# Example token request
curl -X POST \
  https://dev-authprovider.apphero.io/oauth2/token \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=client_credentials&client_id={CLIENT_ID}&client_secret={CLIENT_SECRET}&scope=x-api-key_ie8fbhpkCJ34nu8aUMH3L2lAllizvYvb19t7pioO/read'
```

### API Usage

```bash
# Example API call
curl -X GET \
  https://dev-api.guseip.io/ibat/studentapplications \
  -H 'Authorization: Bearer {ACCESS_TOKEN}' \
  -H 'X-API-Key: {API_KEY}'
```

### Integration Points

1. **Lambda Functions**: `gus-eip-services-{environment}`
2. **API Gateway**: `gus-eip-service-{environment}`
3. **Monitoring**: X-Ray tracing enabled
4. **Analytics**: Usage data collected for analysis

## Related Components

### Other OAuth Clients in Same User Pool

1. **AppHero Client**: `apphero-oauth-client-{environment}`
2. **EIP GUS SF Client**: `eip-gus-sf-oauth-client-{environment}`

### Shared Infrastructure

- **User Pool**: `{environment}-ibd-authprovider`
- **Custom Domains**: Shared SSL certificates
- **API Gateway**: Multiple authorizers for different clients
- **Lambda Functions**: Shared backend services

## Troubleshooting

### Common Issues

1. **Token Validation Failures**:
   - Check scope requirements
   - Verify client credentials
   - Ensure correct User Pool ID

2. **API Access Denied**:
   - Validate API key
   - Check usage plan association
   - Verify authorization header format

3. **Environment Mismatches**:
   - Confirm correct User Pool ID for environment
   - Check custom domain configuration
   - Validate API Gateway authorizer settings

### Monitoring and Logging

- **CloudWatch Logs**: API Gateway execution logs
- **X-Ray Tracing**: Request tracing through Lambda
- **Cognito Logs**: Authentication and authorization events
- **Usage Metrics**: API key usage and rate limiting

---

**Document Version**: 1.0  
**Last Updated**: 2025-07-07  
**Maintained By**: EIP Development Team
