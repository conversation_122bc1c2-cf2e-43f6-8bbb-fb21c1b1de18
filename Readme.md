# Terraform Infrastructure Setup

This project contains the Terraform configuration files for setting up the infrastructure in AWS.

## Pre-requisites

1. [Terraform](https://www.terraform.io/downloads.html) installed on your local machine.
2. AWS account with sufficient permissions to create resources.
3. AWS credentials configured on your local machine. Follow this [guide](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-files.html) to set up AWS credentials.

## Manual Setup

1. **Create an S3 Bucket in AWS:** This bucket is used by Terraform to store the state of your resources. Replace `<bucket_name>` with a unique name for your bucket.

    * Sign in to the AWS Management Console and open the Amazon S3 console at https://console.aws.amazon.com/s3/.
    * Choose 'Create bucket'.
    * In the 'Bucket name' field, type your unique bucket name. Ex: `gus-backend-terraform-<<environment>>`
    * Choose 'Create'.

2. **Create a DynamoDB Table in AWS:** This table is used by Terraform for state locking and consistency. The table should have a single attribute of type String named `LockID`.

    * Open the Amazon DynamoDB console at https://console.aws.amazon.com/dynamodb/.
    * Choose 'Create table'.
    * For 'Table name', enter `terraform-<<environment>>-lock-table`.
    * For 'Primary key', enter `LockID`.
    * Leave the 'Use default settings' checkbox selected and choose 'Create'.

## Running the Terraform Code

1. Clone the repository and navigate into the directory.
2. Add the below code in the main.tf file in root directory. This piece of code will be auto generated in the github actions, so do not commit it.

    ```bash
    terraform {
        required_version = ">= 1.5.1"

        backend "s3" {
            bucket = "gus-backend-terraform-dev"
            key    = "terraform.tfstate"
            region = "ap-south-1"
            dynamodb_table = "terraform-dev-lock-table"
            encrypt = true
        }
    }
    ```
3. Initialize the Terraform directory:

    ```bash
    terraform init
    ```

3. Create the workspace based on the environment (Example: dev, qa):

    ```bash
    terraform workspace new dev
    ```

    If the workspace already exists, then

    ```bash
    terraform workspace select dev
    ```

4. Validate the configuration:

    ```bash
    terraform validate
    ```

5. Check the changes to be applied:

    ```bash
    terraform plan -lock=false -var-file=dev.tfvars
    ```

6. Apply the changes:

    ```bash
    terraform apply -lock=false -var-file=dev.tfvars -auto-approve
    ```

To destroy the resources when they are no longer needed, run:

    ```bash
    terraform destroy -lock=false -var-file=dev.tfvars -auto-approve
    ```

